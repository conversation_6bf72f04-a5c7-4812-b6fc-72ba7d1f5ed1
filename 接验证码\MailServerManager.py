#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件服务器管理器
自动启动和管理py服务器
提供验证码获取API接口
"""

import requests
import logging
import time
import sys
import os
from logging.handlers import RotatingFileHandler

# 自定义日志处理器，兼容GUI环境
class SafeHandler(logging.Handler):
    """安全的日志处理器，兼容GUI和控制台环境"""

    def __init__(self):
        super().__init__()
        self.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

    def emit(self, record):
        try:
            msg = self.format(record)
            # 使用print函数，它会被GUI的TextRedirector捕获
            print(msg)
        except Exception:
            # 如果print失败，静默忽略
            pass

# 设置日志 - 兼容打包后的exe文件和GUI环境
def setup_logger():
    """设置日志配置，兼容打包后的exe文件和GUI环境"""
    logger = logging.getLogger(__name__)

    # 如果已经配置过，直接返回
    if logger.handlers:
        return logger

    logger.setLevel(logging.INFO)

    # 文件处理器 - 总是可用
    try:
        # 确保日志目录存在
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'logs')
        os.makedirs(log_dir, exist_ok=True)

        log_file = os.path.join(log_dir, 'mail_server.log')
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=3,
            encoding='utf-8'
        )
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)
    except Exception:
        pass  # 如果文件日志失败，继续运行

    # 安全的控制台处理器 - 兼容GUI和控制台
    try:
        safe_handler = SafeHandler()
        logger.addHandler(safe_handler)
    except Exception:
        pass  # 如果添加处理器失败，继续运行

    return logger

logger = setup_logger()


class MailServerManager:
    """邮件服务器管理器"""
    
    def __init__(self, server_host: str = "**************", server_port: int = 8603):
        """
        初始化远程邮件服务器管理器

        Args:
            server_host: 远程服务器主机地址，默认**************
            server_port: 远程服务器端口，默认8603
        """
        self.server_host = server_host
        self.server_port = server_port
        self.server_url = f"http://{self.server_host}:{self.server_port}"
        self.is_started = False  # 初始状态为未连接
        
    def start_server(self) -> bool:
        """
        检查远程邮件服务器连接

        Returns:
            bool: 连接成功返回True，失败返回False
        """
        logger.info(f"🔗 检查远程邮件服务器: {self.server_url}")

        if self.is_server_running():
            logger.info("✅ 远程邮件服务器连接成功")
            self.is_started = True
            return True
        else:
            logger.error(f"❌ 无法连接到远程邮件服务器: {self.server_url}")
            return False
    
    def is_server_running(self) -> bool:
        """
        检查服务器是否在运行

        Returns:
            bool: 服务器运行中返回True，否则返回False
        """
        try:
            response = requests.post(
                f"{self.server_url}/api/verification-code",
                json={"email": "<EMAIL>"},  # 使用测试邮箱进行健康检查
                timeout=3
            )
            return response.status_code in [200, 400]
        except:
            return False
    
    def stop_server(self):
        """重置连接状态"""
        self.is_started = False
    
    def get_verification_code(self, email: str, max_wait_time: int = 40) -> str:
        """
        获取验证码 - 在指定时间内每隔1秒尝试获取

        Args:
            email: 邮箱地址
            max_wait_time: 最大等待时间（秒），默认40秒

        Returns:
            str: 验证码字符串，失败返回空字符串
        """
        if not self.is_started or not self.is_server_running():
            logger.error("❌ 邮件服务器未运行，无法获取验证码")
            return ""

        logger.info(f"🔍 开始获取邮箱 {email} 的验证码，最大等待时间: {max_wait_time}秒")

        start_time = time.time()
        attempt = 0

        while time.time() - start_time < max_wait_time:
            attempt += 1
            attempt_start_time = time.time()

            try:
                # logger.info(f"第{attempt}次尝试获取验证码...")

                response = requests.post(
                    f"{self.server_url}/api/verification-code",
                    json={"email": email},
                    timeout=5  # 单次请求超时5秒
                )

                if response.status_code == 200:
                    code = response.text.strip()
                    if code:
                        elapsed_time = int(time.time() - start_time)
                        logger.info(f"✅ 成功获取验证码: {code} (耗时: {elapsed_time}秒)")
                        return code
                    else:
                        logger.info(f"ℹ️  第{attempt}次尝试未获取到验证码，继续等待...")
                else:
                    logger.warning(f"⚠️  API请求失败，状态码: {response.status_code}")

            except requests.exceptions.Timeout:
                logger.warning(f"⚠️  第{attempt}次请求超时，继续尝试...")
            except Exception as e:
                logger.warning(f"⚠️  第{attempt}次请求异常: {e}，继续尝试...")

            # 计算本次尝试耗时，确保总间隔为1秒
            attempt_duration = time.time() - attempt_start_time
            sleep_time = max(0, 1 - attempt_duration)

            # 检查是否还有足够时间进行下次尝试
            if time.time() - start_time + sleep_time >= max_wait_time:
                break

            if sleep_time > 0:
                time.sleep(sleep_time)

        # 超时未获取到验证码
        total_time = int(time.time() - start_time)
        logger.error(f"❌ 获取验证码超时，总尝试时间: {total_time}秒，尝试次数: {attempt}")
        return ""
    
    def get_server_status(self) -> dict:
        """
        获取远程服务器状态信息

        Returns:
            dict: 服务器状态信息
        """
        return {
            'server_url': self.server_url,
            'server_host': self.server_host,
            'server_port': self.server_port,
            'is_running': self.is_server_running(),
            'mode': 'remote'
        }
    
    def restart_server(self) -> bool:
        """
        重新检查远程邮件服务器连接

        Returns:
            bool: 重连成功返回True，失败返回False
        """
        logger.info("🔄 重新检查远程邮件服务器连接...")
        return self.start_server()
    
    def __del__(self):
        """析构函数"""
        pass


def main():
    """测试函数"""
    print("=" * 60)
    print("远程邮件服务器管理器测试")
    print("=" * 60)

    # 创建邮件服务器管理器
    mail_server = MailServerManager()

    try:
        # 测试连接远程服务器
        print("1. 测试连接远程邮件服务器...")
        if mail_server.start_server():
            print("✅ 远程邮件服务器连接成功")
            
            # 显示服务器状态
            status = mail_server.get_server_status()
            print(f"\n2. 服务器状态信息:")
            for key, value in status.items():
                print(f"   {key}: {value}")
            
            # 测试获取验证码
            print(f"\n3. 测试获取验证码...")
            test_email = "<EMAIL>"
            code = mail_server.get_verification_code(test_email, max_wait_time=40)
            
            if code:
                print(f"✅ 获取到验证码: {code}")
            else:
                print("ℹ️  未获取到验证码（这是正常的，因为没有测试邮件）")
            
            print(f"\n4. 断开连接...")
            mail_server.stop_server()
            print("✅ 测试完成")

        else:
            print("❌ 远程邮件服务器连接失败")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断测试")
        mail_server.stop_server()
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        mail_server.stop_server()


if __name__ == "__main__":
    main()
